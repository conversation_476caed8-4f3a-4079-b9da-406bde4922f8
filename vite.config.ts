import { defineConfig, type AliasOptions } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";


// @ts-expect-error This is a dumb type
import path from "path";

// @ts-expect-error This is a dumb type
const root = path.resolve(__dirname, "src");

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
    resolve: {
    alias: {
      "@": root,
    } as AliasOptions,
  },
});
