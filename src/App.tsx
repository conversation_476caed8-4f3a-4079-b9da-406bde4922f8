import { useState, useEffect } from "react";
import { Upload, Image, Type, Music, Filter } from "lucide-react";
import type { Post, Challenge } from "@/data/mockData";
import { getPosts, getCurrentChallenge, savePost } from "./services/mockApi";
import PostCard from "@/components/PostCard";
import UploadModal from "@/components/UploadModal";
import ChallengeCard from "@/components/ChallengeCard";
import ImageEditor from "@/components/ImageEditor";
import TextEditor from "@/components/TextEditor";
import AudioEditor from "@/components/AudioEditor";
import TestPage from "@/pages/TestPage";
import ErrorBoundary from "@/components/ErrorBoundary";
import "./App.css";

type FilterType = "all" | "image" | "text" | "audio";

function App() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [filter, setFilter] = useState<FilterType>("all");
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [challenge, setChallenge] = useState<Challenge | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingPost, setEditingPost] = useState<Post | null>(null);
  const [currentPage, setCurrentPage] = useState<"feed" | "test">("feed");

  useEffect(() => {
    loadPosts();
    loadChallenge();
  }, [filter]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadPosts = async () => {
    setLoading(true);
    try {
      const filterType = filter === "all" ? undefined : filter;
      const data = await getPosts(filterType);
      setPosts(data);
    } catch (error) {
      console.error("Failed to load posts:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadChallenge = async () => {
    try {
      const challengeData = await getCurrentChallenge();
      setChallenge(challengeData);
    } catch (error) {
      console.error("Failed to load challenge:", error);
    }
  };

  const handleUploadSuccess = () => {
    setIsUploadModalOpen(false);
    loadPosts(); // Refresh posts after upload
  };

  const handleRemix = (post: Post) => {
    setEditingPost(post);
  };

  const handleImageSave = async (editedImageData: string) => {
    if (!editingPost) return;

    try {
      // In a real app, we'd upload the edited image
      // For now, we'll just create a new post with the data URL
      await savePost({
        type: "image",
        content: editedImageData,
        title: `${editingPost.title} (Remixed)`,
        author: "Anonymous",
        remixCount: 0,
        isRemix: true,
        parentId: editingPost.id,
        metadata: {
          originalDimensions: { width: 800, height: 600 },
          effects: ["custom_edit"],
        },
      });

      setEditingPost(null);
      loadPosts();
    } catch (error) {
      console.error("Failed to save remix:", error);
    }
  };

  const handleTextSave = async (
    editedText: string,
    effects: string[],
    audioTrack?: string
  ) => {
    if (!editingPost) return;

    try {
      await savePost({
        type: "text",
        content: editedText,
        title: `${editingPost.title} (Remixed)`,
        author: "Anonymous",
        remixCount: 0,
        isRemix: true,
        parentId: editingPost.id,
        metadata: {
          effects,
          audioTrack,
        },
      });

      setEditingPost(null);
      loadPosts();
    } catch (error) {
      console.error("Failed to save text remix:", error);
    }
  };

  const handleAudioSave = async (
    remixedAudioData: string,
    effects: string[]
  ) => {
    if (!editingPost) return;

    try {
      await savePost({
        type: "audio",
        content: remixedAudioData,
        title: `${editingPost.title} (Remixed)`,
        author: "Anonymous",
        remixCount: 0,
        isRemix: true,
        parentId: editingPost.id,
        metadata: {
          effects,
          duration: 30, // Default duration
        },
      });

      setEditingPost(null);
      loadPosts();
    } catch (error) {
      console.error("Failed to save audio remix:", error);
    }
  };

  const handleJoinChallenge = (challenge: Challenge) => {
    // Create a mock post for the challenge base content
    const challengePost: Post = {
      id: `challenge-${challenge.id}`,
      type: challenge.type,
      content: challenge.baseContent,
      title: challenge.title,
      author: "Challenge",
      createdAt: new Date(),
      remixCount: 0,
      isRemix: false,
    };

    setEditingPost(challengePost);
  };

  if (currentPage === "test") {
    return <TestPage onBack={() => setCurrentPage("feed")} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">RemixHub</h1>
            </div>

            <button
              onClick={() => setIsUploadModalOpen(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-1 md:space-x-2 transition-colors"
            >
              <Upload size={18} className="md:w-5 md:h-5" />
              <span className="hidden sm:inline ">Upload</span>
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6 w-fit overflow-x-auto">
          <button
            onClick={() => setFilter("all")}
            className={`px-3 md:px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              filter === "all"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <Filter size={16} className="inline mr-1 md:mr-2" />
            <span className="hidden sm:inline">All</span>
          </button>
          <button
            onClick={() => setFilter("image")}
            className={`px-3 md:px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              filter === "image"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <Image size={16} className="inline mr-1 md:mr-2" />
            <span className="hidden sm:inline">Images</span>
          </button>
          <button
            onClick={() => setFilter("text")}
            className={`px-3 md:px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              filter === "text"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <Type size={16} className="inline mr-1 md:mr-2" />
            <span className="hidden sm:inline">Text</span>
          </button>
          <button
            onClick={() => setFilter("audio")}
            className={`px-3 md:px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              filter === "audio"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <Music size={16} className="inline mr-1 md:mr-2" />
            <span className="hidden sm:inline">Audio</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Feed */}
          <div className="lg:col-span-2">
            {loading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="bg-white rounded-lg p-6 animate-pulse"
                  >
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-32 bg-gray-200 rounded mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : posts.length === 0 ? (
              <div className="bg-white rounded-lg p-8 text-center">
                <p className="text-gray-500">
                  No posts found. Be the first to upload something!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {posts.map((post) => (
                  <PostCard key={post.id} post={post} onRemix={handleRemix} />
                ))}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Weekly Challenge */}
            {challenge && (
              <ChallengeCard
                challenge={challenge}
                onJoinChallenge={handleJoinChallenge}
              />
            )}

            {/* Trending Hashtags */}
            <div className="bg-white rounded-lg p-6">
              <h3 className="font-bold text-lg mb-4">Trending</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-purple-600 font-medium">
                    #SpookyRemix
                  </span>
                  <span className="text-gray-500 text-sm">42 posts</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-600 font-medium">
                    #TextEffects
                  </span>
                  <span className="text-gray-500 text-sm">28 posts</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-600 font-medium">
                    #AudioMash
                  </span>
                  <span className="text-gray-500 text-sm">15 posts</span>
                </div>
              </div>
            </div>

            {/* Feature Tests Link */}
            <div className="bg-white rounded-lg p-6">
              <h3 className="font-bold text-lg mb-4">System Diagnostics</h3>
              <p className="text-gray-600 text-sm mb-4">
                Run comprehensive tests to verify all features are working
                correctly.
              </p>
              <button
                onClick={() => setCurrentPage("test")}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors"
              >
                Run Feature Tests
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Modal */}
      {isUploadModalOpen && (
        <UploadModal
          onClose={() => setIsUploadModalOpen(false)}
          onSuccess={handleUploadSuccess}
        />
      )}

      {/* Image Editor */}
      {editingPost && editingPost.type === "image" && (
        <ErrorBoundary
          fallback={
            <div className="fixed inset-0 bg-white/20 backdrop-blur-md flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 max-w-md">
                <h3 className="text-lg font-bold mb-4">Image Editor Error</h3>
                <p className="text-gray-600 mb-4">
                  The image editor encountered an error. Please try again.
                </p>
                <button
                  onClick={() => setEditingPost(null)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
                >
                  Close
                </button>
              </div>
            </div>
          }
        >
          <ImageEditor
            imageUrl={editingPost.content}
            onSave={handleImageSave}
            onClose={() => setEditingPost(null)}
          />
        </ErrorBoundary>
      )}

      {/* Text Editor */}
      {editingPost && editingPost.type === "text" && (
        <TextEditor
          originalText={editingPost.content}
          onSave={handleTextSave}
          onClose={() => setEditingPost(null)}
        />
      )}

      {/* Audio Editor */}
      {editingPost && editingPost.type === "audio" && (
        <AudioEditor
          originalAudioUrl={editingPost.content}
          onSave={handleAudioSave}
          onClose={() => setEditingPost(null)}
        />
      )}
    </div>
  );
}

export default App;
