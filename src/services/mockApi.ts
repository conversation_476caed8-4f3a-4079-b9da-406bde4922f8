import { type Post, type Challenge, mockPosts, currentChallenge } from '../data/mockData';

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Local storage keys
const POSTS_KEY = 'remixhub_posts';
const UPLOADS_KEY = 'remixhub_uploads';

// Initialize local storage with mock data if empty
const initializeStorage = () => {
  if (!localStorage.getItem(POSTS_KEY)) {
    localStorage.setItem(POSTS_KEY, JSON.stringify(mockPosts));
  }
  if (!localStorage.getItem(UPLOADS_KEY)) {
    localStorage.setItem(UPLOADS_KEY, JSON.stringify([]));
  }
};

// Get all posts from local storage
export const getPosts = async (filter?: 'image' | 'text' | 'audio'): Promise<Post[]> => {
  await delay(300); // Simulate network delay
  initializeStorage();
  
  const posts = JSON.parse(localStorage.getItem(POSTS_KEY) || '[]');
  
  if (filter) {
    return posts.filter((post: Post) => post.type === filter);
  }
  
  return posts.sort((a: Post, b: Post) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

// Get a single post by ID
export const getPost = async (id: string): Promise<Post | null> => {
  await delay(200);
  initializeStorage();
  
  const posts = JSON.parse(localStorage.getItem(POSTS_KEY) || '[]');
  return posts.find((post: Post) => post.id === id) || null;
};

// Save a new post (for uploads and remixes)
export const savePost = async (post: Omit<Post, 'id' | 'createdAt'>): Promise<Post> => {
  await delay(500);
  initializeStorage();
  
  const posts = JSON.parse(localStorage.getItem(POSTS_KEY) || '[]');
  const newPost: Post = {
    ...post,
    id: Date.now().toString(),
    createdAt: new Date(),
  };
  
  posts.unshift(newPost);
  localStorage.setItem(POSTS_KEY, JSON.stringify(posts));
  
  return newPost;
};

// Get current weekly challenge
export const getCurrentChallenge = async (): Promise<Challenge> => {
  await delay(200);
  return currentChallenge;
};

// Simulate file upload (stores file as base64 in localStorage)
export const uploadFile = async (file: File): Promise<string> => {
  await delay(1000); // Simulate upload time
  
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result as string;
      const uploads = JSON.parse(localStorage.getItem(UPLOADS_KEY) || '[]');
      const fileId = Date.now().toString();
      
      uploads.push({
        id: fileId,
        filename: file.name,
        data: base64,
        type: file.type,
        uploadedAt: new Date().toISOString()
      });
      
      localStorage.setItem(UPLOADS_KEY, JSON.stringify(uploads));
      resolve(`/uploads/${fileId}`);
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

// Get uploaded file data
export const getUploadedFile = (fileId: string): string | null => {
  const uploads = JSON.parse(localStorage.getItem(UPLOADS_KEY) || '[]');
  const upload = uploads.find((u: any) => u.id === fileId);
  return upload ? upload.data : null;
};

// Simulate duplicate detection (basic implementation)
export const checkForDuplicates = async (content: string, type: string): Promise<boolean> => {
  await delay(300);
  initializeStorage();
  
  const posts = JSON.parse(localStorage.getItem(POSTS_KEY) || '[]');
  
  if (type === 'text') {
    // Simple text similarity check (20% difference minimum)
    return posts.some((post: Post) => {
      if (post.type !== 'text') return false;
      const similarity = calculateTextSimilarity(content, post.content);
      return similarity > 0.8; // 80% similar = too similar
    });
  }
  
  // For images and audio, we'll just check exact matches for now
  return posts.some((post: Post) => post.content === content);
};

// Simple text similarity calculation (Jaccard similarity)
const calculateTextSimilarity = (text1: string, text2: string): number => {
  const words1 = new Set(text1.toLowerCase().split(/\s+/));
  const words2 = new Set(text2.toLowerCase().split(/\s+/));
  
  const intersection = new Set([...words1].filter(x => words2.has(x)));
  const union = new Set([...words1, ...words2]);
  
  return intersection.size / union.size;
};

// Get remix lineage (parent and children)
export const getRemixLineage = async (postId: string): Promise<{parent?: Post, children: Post[]}> => {
  await delay(200);
  initializeStorage();
  
  const posts = JSON.parse(localStorage.getItem(POSTS_KEY) || '[]');
  const post = posts.find((p: Post) => p.id === postId);
  
  if (!post) return { children: [] };
  
  const parent = post.parentId ? posts.find((p: Post) => p.id === post.parentId) : undefined;
  const children = posts.filter((p: Post) => p.parentId === postId);
  
  return { parent, children };
};
