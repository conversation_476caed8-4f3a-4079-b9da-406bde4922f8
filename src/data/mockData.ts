export interface Post {
  id: string;
  type: "image" | "text" | "audio";
  content: string; // URL for images/audio, text content for text posts
  title: string;
  parentId?: string; // For remixes
  createdAt: Date;
  author: string;
  remixCount: number;
  isRemix: boolean;
  metadata?: {
    originalDimensions?: { width: number; height: number };
    duration?: number; // for audio
    effects?: string[]; // applied effects
    audioTrack?: string; // for cross-content remixing
  };
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  baseContent: string; // URL or text
  type: "image" | "text" | "audio";
  endDate: Date;
  participantCount: number;
}

// Sample images (using placeholder URLs)
export const mockPosts: Post[] = [
  {
    id: "1",
    type: "image",
    content: "https://picsum.photos/400/300?random=1",
    title: "Sunset over mountains",
    createdAt: new Date("2024-01-15"),
    author: "NaturePhotog",
    remixCount: 12,
    isRemix: false,
    metadata: {
      originalDimensions: { width: 400, height: 300 },
    },
  },
  {
    id: "2",
    type: "text",
    content: "When life gives you lemons, make lemonade! 🍋",
    title: "Motivational Monday",
    createdAt: new Date("2024-01-14"),
    author: "PositiveVibes",
    remixCount: 8,
    isRemix: false,
  },
  {
    id: "3",
    type: "image",
    content: "https://picsum.photos/400/300?random=2",
    title: "Spooky Forest (Remixed)",
    parentId: "1",
    createdAt: new Date("2024-01-16"),
    author: "SpookyArtist",
    remixCount: 3,
    isRemix: true,
    metadata: {
      originalDimensions: { width: 400, height: 300 },
      effects: ["dark_filter", "fog_overlay", "spooky_text"],
    },
  },
  {
    id: "4",
    type: "text",
    content: "When life gives you lemons, THROW THEM BACK! 🍋💥",
    title: "Motivational Monday (Chaos Edition)",
    parentId: "2",
    createdAt: new Date("2024-01-15"),
    author: "ChaosGremlin",
    remixCount: 15,
    isRemix: true,
    metadata: {
      effects: ["rainbow_text", "shake_animation"],
    },
  },
  {
    id: "5",
    type: "image",
    content: "https://picsum.photos/400/300?random=3",
    title: "Cat wearing a wizard hat",
    createdAt: new Date("2024-01-13"),
    author: "CatLover99",
    remixCount: 25,
    isRemix: false,
    metadata: {
      originalDimensions: { width: 400, height: 300 },
    },
  },
  {
    id: "6",
    type: "audio",
    content: "/audio/sample-beat.mp3", // We'll create this later
    title: "Chill Lo-Fi Beat",
    createdAt: new Date("2024-01-12"),
    author: "BeatMaker",
    remixCount: 7,
    isRemix: false,
    metadata: {
      duration: 45,
    },
  },
  {
    id: "7",
    type: "text",
    content:
      "Just had the BEST coffee ever! ☕️✨ Sometimes it's the little things that make your day perfect.",
    title: "Coffee Appreciation Post",
    createdAt: new Date("2024-01-17"),
    author: "CoffeeLover",
    remixCount: 4,
    isRemix: false,
  },
  {
    id: "8",
    type: "image",
    content: "https://picsum.photos/400/300?random=4",
    title: "My cat discovered the printer",
    createdAt: new Date("2024-01-18"),
    author: "CatParent",
    remixCount: 18,
    isRemix: false,
    metadata: {
      originalDimensions: { width: 400, height: 300 },
    },
  },
  {
    id: "9",
    type: "text",
    content:
      "Just had the WORST coffee ever! ☕️💀 Sometimes it's the little things that RUIN your day completely!!!",
    title: "Coffee Appreciation Post (Chaos Remix)",
    parentId: "7",
    createdAt: new Date("2024-01-18"),
    author: "ChaosGremlin",
    remixCount: 2,
    isRemix: true,
    metadata: {
      effects: ["shake_animation", "glow_text"],
    },
  },
];

export const currentChallenge: Challenge = {
  id: "challenge-1",
  title: "Make This Photo Spooky! 👻",
  description:
    "Take this peaceful landscape and turn it into something that would fit in a horror movie. Add fog, change colors, add spooky elements - let your creativity run wild!",
  baseContent: "https://picsum.photos/500/400?random=10",
  type: "image",
  endDate: new Date("2024-01-21"),
  participantCount: 42,
};

// Sample audio tracks for remixing (using placeholder URLs for demo)
export const sampleAudioTracks = [
  {
    id: "bg1",
    name: "Upbeat Pop",
    url: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    duration: 30,
  },
  {
    id: "bg2",
    name: "Chill Vibes",
    url: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    duration: 25,
  },
  {
    id: "bg3",
    name: "Epic Orchestral",
    url: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    duration: 40,
  },
];

// Sticker assets for image editing
export const stickerAssets = [
  {
    id: "hat1",
    name: "Wizard Hat",
    url: "/stickers/wizard-hat.png",
    category: "hats",
  },
  {
    id: "hat2",
    name: "Top Hat",
    url: "/stickers/top-hat.png",
    category: "hats",
  },
  {
    id: "glasses1",
    name: "Cool Sunglasses",
    url: "/stickers/sunglasses.png",
    category: "accessories",
  },
  {
    id: "emoji1",
    name: "Fire Emoji",
    url: "/stickers/fire-emoji.png",
    category: "emojis",
  },
  {
    id: "emoji2",
    name: "Laughing Emoji",
    url: "/stickers/laugh-emoji.png",
    category: "emojis",
  },
];
