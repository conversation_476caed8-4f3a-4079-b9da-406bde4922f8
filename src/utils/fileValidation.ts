// File validation utilities

export const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif'];
export const ALLOWED_AUDIO_TYPES = ['audio/mp3', 'audio/wav', 'audio/mpeg'];
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
export const MAX_AUDIO_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_AUDIO_DURATION = 30; // 30 seconds
export const MAX_TEXT_LENGTH = 500;

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export const validateImageFile = (file: File): ValidationResult => {
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: 'Only JPEG, PNG, and GIF images are allowed'
    };
  }
  
  if (file.size > MAX_IMAGE_SIZE) {
    return {
      isValid: false,
      error: 'Image must be smaller than 5MB'
    };
  }
  
  return { isValid: true };
};

export const validateAudioFile = (file: File): ValidationResult => {
  if (!ALLOWED_AUDIO_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: 'Only MP3 and WAV audio files are allowed'
    };
  }
  
  if (file.size > MAX_AUDIO_SIZE) {
    return {
      isValid: false,
      error: 'Audio file must be smaller than 10MB'
    };
  }
  
  return { isValid: true };
};

export const validateTextContent = (text: string): ValidationResult => {
  if (text.length === 0) {
    return {
      isValid: false,
      error: 'Text content cannot be empty'
    };
  }
  
  if (text.length > MAX_TEXT_LENGTH) {
    return {
      isValid: false,
      error: `Text must be ${MAX_TEXT_LENGTH} characters or less`
    };
  }
  
  return { isValid: true };
};

// Get audio duration (returns a promise)
export const getAudioDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(file);
    
    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration);
    });
    
    audio.addEventListener('error', () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load audio file'));
    });
    
    audio.src = url;
  });
};

// Validate audio duration
export const validateAudioDuration = async (file: File): Promise<ValidationResult> => {
  try {
    const duration = await getAudioDuration(file);
    
    if (duration > MAX_AUDIO_DURATION) {
      return {
        isValid: false,
        error: `Audio must be ${MAX_AUDIO_DURATION} seconds or less`
      };
    }
    
    return { isValid: true };
  } catch (error) {
    console.error('Error validating audio duration:', error);
    return {
      isValid: false,
      error: 'Failed to validate audio duration'
    };
  }
};
