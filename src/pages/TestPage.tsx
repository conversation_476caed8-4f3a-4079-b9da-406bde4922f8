import { ArrowLeft } from 'lucide-react';
import TestFeatures from '../components/TestFeatures';

interface TestPageProps {
  onBack: () => void;
}

const TestPage = ({ onBack }: TestPageProps) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} />
              <span>Back to Feed</span>
            </button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">RemixHub - Feature Tests</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">System Diagnostics</h2>
          <p className="text-gray-600 text-lg">
            Run comprehensive tests to verify all RemixHub features are working correctly. 
            This includes checking browser compatibility, API functionality, and core features.
          </p>
        </div>

        <TestFeatures />

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">About These Tests</h3>
          <div className="text-blue-800 space-y-2">
            <p>• <strong>Mock Data Loading:</strong> Verifies sample content loads properly</p>
            <p>• <strong>Local Storage:</strong> Tests browser storage for saving user data</p>
            <p>• <strong>File Validation:</strong> Checks upload validation systems</p>
            <p>• <strong>Web Audio API:</strong> Tests audio remix capabilities</p>
            <p>• <strong>Media Recording:</strong> Verifies microphone access for voice recording</p>
          </div>
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-3">Troubleshooting</h3>
          <div className="text-yellow-800 space-y-2">
            <p>• If tests fail, try refreshing the page</p>
            <p>• Media recording requires HTTPS in production</p>
            <p>• Some features may not work in private/incognito mode</p>
            <p>• Clear browser cache if you encounter persistent issues</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
