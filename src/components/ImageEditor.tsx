import { useEffect, useRef, useState, useCallback } from "react";
import { Brush, Type, Download, Undo } from "lucide-react";

// Types
interface ImageEditorProps {
  imageUrl: string;
  onSave: (editedImageData: string) => void;
  onClose: () => void;
}

interface TextElement {
  id: string;
  x: number;
  y: number;
  text: string;
  color: string;
  fontSize: number;
  isEditing: boolean;
  width: number;
  height: number;
  isFinalized: boolean;
}

// EditorToolbar Component
interface EditorToolbarProps {
  tool: "brush" | "text";
  setTool: (tool: "brush" | "text") => void;
  brushColor: string;
  setBrushColor: (color: string) => void;
  brushSize: number;
  setBrushSize: (size: number) => void;
  isShiftPressed: boolean;
  onUndo: () => void;
  canUndo: boolean;
  textElements: TextElement[];
  onTextColorChange: (color: string) => void;
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  tool,
  setTool,
  brushColor,
  setBrushColor,
  brushSize,
  setBrushSize,
  isShiftPressed,
  onUndo,
  canUndo,
  textElements,
  onTextColorChange,
}) => {
  const colors = [
    "#ff0000",
    "#00ff00",
    "#0000ff",
    "#ffff00",
    "#ff00ff",
    "#00ffff",
    "#000000",
    "#ffffff",
    "#808080",
    "#ffa500",
    "#800080",
    "#008000",
  ];

  return (
    <div
      className="w-full md:w-64 p-3 md:p-4 border-b md:border-r md:border-b-0 bg-gray-50 space-y-3 md:space-y-4 max-h-48 md:max-h-none overflow-y-auto md:overflow-y-visible"
      data-toolbar
    >
      <div className="space-y-2">
        <h3 className="font-semibold text-sm">Tools</h3>
        <div className="space-y-1">
          <button
            onClick={() => setTool("brush")}
            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-left ${
              tool === "brush"
                ? "bg-purple-100 text-purple-700"
                : "text-gray-700 hover:bg-gray-100"
            }`}
          >
            <Brush size={16} />
            <span>Brush</span>
          </button>
          <button
            onClick={() => setTool("text")}
            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-left ${
              tool === "text"
                ? "bg-purple-100 text-purple-700"
                : "text-gray-700 hover:bg-gray-100"
            }`}
          >
            <Type size={16} />
            <span>Text</span>
          </button>
        </div>
      </div>

      {tool === "text" && (
        <div
          className={`border rounded-lg p-3 ${
            isShiftPressed
              ? "bg-orange-50 border-orange-200"
              : "bg-blue-50 border-blue-200"
          }`}
        >
          <h4
            className={`font-semibold text-xs mb-1 ${
              isShiftPressed ? "text-orange-800" : "text-blue-800"
            }`}
          >
            {isShiftPressed
              ? "MOVE MODE - Click text to move"
              : "Text Tool Instructions:"}
          </h4>
          {!isShiftPressed && (
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Drag to select text area</li>
              <li>• Click existing text to re-edit</li>
              <li>• Hold Shift to enter move mode</li>
              <li>• Adjust font size in editor</li>
            </ul>
          )}
          {isShiftPressed && (
            <p className="text-xs text-orange-700">
              Click on any text to start moving it around. Release Shift to
              return to normal mode.
            </p>
          )}
        </div>
      )}

      {/* Text Color Selector */}
      {tool === "text" && (
        <div className="space-y-2 color-selector" data-toolbar>
          <h4 className="font-semibold text-sm">Text Color</h4>
          <div className="grid grid-cols-6 gap-1">
            {colors.map((color) => (
              <button
                key={color}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent click from bubbling up and closing edit window
                  setBrushColor(color);
                  // Apply color to currently editing text
                  const editingText = textElements.find((el) => el.isEditing);
                  if (editingText) {
                    onTextColorChange(color);
                  }
                }}
                className={`w-6 h-6 rounded border-2 ${
                  brushColor === color ? "border-gray-800" : "border-gray-300"
                }`}
                style={{ backgroundColor: color }}
                title={`Select ${color} color`}
              />
            ))}
          </div>
        </div>
      )}

      {tool === "brush" && (
        <div className="space-y-2">
          <h3 className="font-semibold text-sm">Brush Settings</h3>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Size</label>
            <input
              type="range"
              min="1"
              max="50"
              value={brushSize}
              onChange={(e) => setBrushSize(Number(e.target.value))}
              className="w-full"
            />
            <span className="text-xs text-gray-600">{brushSize}px</span>
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Color</label>
            <div className="grid grid-cols-6 gap-1">
              {colors.map((color) => (
                <button
                  key={color}
                  onClick={() => setBrushColor(color)}
                  className={`w-6 h-6 rounded border-2 ${
                    brushColor === color ? "border-gray-800" : "border-gray-300"
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      <div className="space-y-2">
        <h3 className="font-semibold text-sm">Actions</h3>
        <button
          onClick={onUndo}
          disabled={!canUndo}
          className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Undo size={16} />
          <span>Undo</span>
        </button>
      </div>
    </div>
  );
};

// TextIndicator Component
interface TextIndicatorProps {
  textEl: TextElement;
  isShiftPressed: boolean;
  movingTextId: string | null;
  canvasRef: React.RefObject<HTMLCanvasElement>;
  onStartMove: (id: string, pos: { x: number; y: number }) => void;
  onEdit: (id: string) => void;
  justFinishedMoving: boolean;
}

const TextIndicator: React.FC<TextIndicatorProps> = ({
  textEl,
  isShiftPressed,
  movingTextId,
  canvasRef,
  onStartMove,
  onEdit,
  justFinishedMoving,
}) => {
  // Only show indicators when shift is pressed AND text is finalized (not editing)
  if (
    textEl.isEditing ||
    !textEl.isFinalized ||
    (!isShiftPressed && movingTextId !== textEl.id)
  )
    return null;

  const canvas = canvasRef.current;
  if (!canvas) return null;

  const canvasRect = canvas.getBoundingClientRect();
  const containerRect = canvas.parentElement?.getBoundingClientRect();
  if (!containerRect) return null;

  const { x: overlayX, y: overlayY } = {
    x:
      (textEl.x / canvas.width) * canvasRect.width +
      (canvasRect.left - containerRect.left),
    y:
      ((textEl.y - (textEl.height || 30)) / canvas.height) * canvasRect.height +
      (canvasRect.top - containerRect.top),
  };
  const overlayWidth =
    ((textEl.width || 100) / canvas.width) * canvasRect.width;
  const overlayHeight =
    ((textEl.height || 30) / canvas.height) * canvasRect.height;

  return (
    <div
      className={`absolute border-2 cursor-pointer transition-all ${
        movingTextId === textEl.id
          ? "border-solid border-blue-600"
          : "border-dashed border-blue-500 hover:border-blue-600"
      }`}
      style={{
        left: overlayX,
        top: overlayY,
        width: overlayWidth,
        height: overlayHeight,
        zIndex: movingTextId === textEl.id ? 8 : 3,
        backgroundColor: "transparent",
      }}
      title={
        movingTextId === textEl.id
          ? "Moving text..."
          : "Shift+Click to move, Click to edit"
      }
      onClick={(e) => {
        const shiftHeld = e.shiftKey || isShiftPressed;
        if (shiftHeld) {
          const pos = {
            x:
              ((e.clientX - canvasRect.left) / canvasRect.width) * canvas.width,
            y:
              ((e.clientY - canvasRect.top) / canvasRect.height) *
              canvas.height,
          };
          onStartMove(textEl.id, pos);
        } else if (!justFinishedMoving) {
          onEdit(textEl.id);
        }
        e.stopPropagation();
      }}
    />
  );
};

// TextEditorOverlay Component
interface TextEditorOverlayProps {
  textEl: TextElement;
  canvasRef: React.RefObject<HTMLCanvasElement>;
  onUpdate: (id: string, updates: Partial<TextElement>) => void;
  onApply: (id: string) => void;
  onDelete: (id: string) => void;
}

const InlineTextEditor: React.FC<TextEditorOverlayProps> = ({
  textEl,
  canvasRef,
  onUpdate,
  onApply,
  onDelete,
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
    originalTopY: number;
    originalX: number;
  } | null>(null);
  const [isMoving, setIsMoving] = useState(false);
  const [moveStart, setMoveStart] = useState<{
    x: number;
    y: number;
    originalX: number;
    originalY: number;
  } | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Add global mouse event listeners for resize and move
  useEffect(() => {
    const handleResizeMove = (e: MouseEvent) => {
      if (!isResizing || !resizeStart) return;

      const deltaX = e.clientX - resizeStart.x;
      const deltaY = e.clientY - resizeStart.y;

      const newWidth = Math.max(resizeStart.width + deltaX, 50);
      const newHeight = Math.max(resizeStart.height + deltaY, 30);

      // Convert back to canvas coordinates
      const canvas = canvasRef.current;
      if (!canvas) return;

      const canvasRect = canvas.getBoundingClientRect();
      const canvasWidth = (newWidth / canvasRect.width) * canvas.width;
      const canvasHeight = (newHeight / canvasRect.height) * canvas.height;

      // Calculate the new Y position to keep top-left fixed
      // We want: originalTopY = newY - newHeight
      // So: newY = originalTopY + newHeight
      const newY = resizeStart.originalTopY + canvasHeight;

      onUpdate(textEl.id, {
        x: resizeStart.originalX, // Keep X fixed
        y: newY, // Adjust Y to maintain fixed top
        width: canvasWidth,
        height: canvasHeight,
      });
    };

    const handleMoveMove = (e: MouseEvent) => {
      if (!isMoving || !moveStart) return;

      const deltaX = e.clientX - moveStart.x;
      const deltaY = e.clientY - moveStart.y;

      // Convert deltas to canvas coordinates
      const canvas = canvasRef.current;
      if (!canvas) return;

      const canvasRect = canvas.getBoundingClientRect();
      const canvasDeltaX = (deltaX / canvasRect.width) * canvas.width;
      const canvasDeltaY = (deltaY / canvasRect.height) * canvas.height;

      const newX = moveStart.originalX + canvasDeltaX;
      const newY = moveStart.originalY + canvasDeltaY;

      onUpdate(textEl.id, {
        x: newX,
        y: newY,
      });
    };

    const handleEnd = () => {
      setIsResizing(false);
      setResizeStart(null);
      setIsMoving(false);
      setMoveStart(null);
    };

    if (isResizing || isMoving) {
      document.addEventListener(
        "mousemove",
        isResizing ? handleResizeMove : handleMoveMove
      );
      document.addEventListener("mouseup", handleEnd);
      return () => {
        document.removeEventListener(
          "mousemove",
          isResizing ? handleResizeMove : handleMoveMove
        );
        document.removeEventListener("mouseup", handleEnd);
      };
    }
  }, [
    isResizing,
    resizeStart,
    isMoving,
    moveStart,
    canvasRef,
    onUpdate,
    textEl.id,
  ]);

  if (!textEl.isEditing) return null;

  const canvas = canvasRef.current;
  if (!canvas) return null;

  const canvasRect = canvas.getBoundingClientRect();
  const containerRect = canvas.parentElement?.getBoundingClientRect();
  if (!containerRect) return null;

  // Position the editor exactly over the text area
  const overlayX =
    (textEl.x / canvas.width) * canvasRect.width +
    (canvasRect.left - containerRect.left);
  const overlayY =
    ((textEl.y - textEl.height) / canvas.height) * canvasRect.height +
    (canvasRect.top - containerRect.top);
  const overlayWidth = (textEl.width / canvas.width) * canvasRect.width;
  const overlayHeight = (textEl.height / canvas.height) * canvasRect.height;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      // Enter without Shift applies the text
      e.preventDefault();
      onApply(textEl.id);
    } else if (e.key === "Enter" && e.shiftKey) {
      // Shift+Enter adds a new line (allow default behavior)
      // Don't prevent default - let the textarea handle it
    } else if (e.key === "Escape") {
      e.preventDefault();
      onDelete(textEl.id);
    }
  };

  const handleClickOutside = (e: React.MouseEvent) => {
    // Check if the click is on a color selector button or toolbar
    const target = e.target as HTMLElement;
    if (target.closest(".color-selector") || target.closest("[data-toolbar]")) {
      return; // Don't close if clicking on toolbar elements
    }

    e.stopPropagation();
    onApply(textEl.id);
  };

  const handleResizeStart = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsResizing(true);

    // Store the original top-left position in canvas coordinates
    const originalTopY = textEl.y - textEl.height;

    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: overlayWidth,
      height: overlayHeight,
      originalTopY: originalTopY,
      originalX: textEl.x,
    });
  };

  const handleMoveStart = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsMoving(true);

    setMoveStart({
      x: e.clientX,
      y: e.clientY,
      originalX: textEl.x,
      originalY: textEl.y,
    });
  };

  return (
    <>
      {/* Invisible overlay to catch clicks outside */}
      <div className="fixed inset-0 z-20" onClick={handleClickOutside} />

      {/* Text editing area */}
      <div
        className="absolute border-2 border-blue-500 z-30"
        style={{
          left: overlayX,
          top: overlayY,
          width: overlayWidth,
          height: overlayHeight,
          minWidth: 100,
          minHeight: 30,
          backgroundColor: "transparent",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <textarea
          ref={textareaRef}
          value={textEl.text}
          onChange={(e) => onUpdate(textEl.id, { text: e.target.value })}
          onKeyDown={handleKeyDown}
          className="w-full h-full resize-none border-none outline-none bg-transparent p-1"
          style={{
            fontSize: `${
              (textEl.fontSize / canvas.height) * canvasRect.height
            }px`,
            color: textEl.color,
            lineHeight: "1.2",
            textAlign: "left", // Changed from center to left for better multi-line editing
            verticalAlign: "top",
          }}
          placeholder="Type here... (Shift+Enter for new line)"
          autoFocus
        />

        {/* Font size controls */}
        <div className="absolute -bottom-8 left-0 bg-white border rounded shadow-lg p-1 flex items-center space-x-2 text-xs">
          <span>Size:</span>
          <input
            type="range"
            min="12"
            max="48"
            value={textEl.fontSize}
            onChange={(e) =>
              onUpdate(textEl.id, { fontSize: Number(e.target.value) })
            }
            className="w-16"
          />
          <span>{textEl.fontSize}px</span>
        </div>

        {/* Move handle */}
        <div
          className="absolute -top-1 -left-1 w-3 h-3 bg-green-500 cursor-move hover:bg-green-600 border border-white"
          onMouseDown={handleMoveStart}
          title="Drag to move"
        />

        {/* Resize handle */}
        <div
          className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 cursor-se-resize hover:bg-blue-600 border border-white"
          onMouseDown={handleResizeStart}
          title="Drag to resize"
        />
      </div>
    </>
  );
};

// EditorCanvas Component
interface EditorCanvasProps {
  imageUrl: string;
  tool: "brush" | "text";
  brushColor: string;
  brushSize: number;
  textElements: TextElement[];
  isShiftPressed: boolean;
  onSave: (dataURL: string) => void;
  onTextUpdate: (id: string, updates: Partial<TextElement>) => void;
  onTextApply: (id: string) => void;
  onTextDelete: (id: string) => void;
  onTextCreate: (text: TextElement) => void;
  onTextMoveStart: (id: string, pos: { x: number; y: number }) => void;
  onTextMove: (id: string, x: number, y: number) => void;
  onTextMoveEnd: () => void;
  onUndo: () => void;
  history: string[];
  historyIndex: number;
}

const EditorCanvas: React.FC<EditorCanvasProps> = ({
  imageUrl,
  tool,
  brushColor,
  brushSize,
  textElements,
  isShiftPressed,
  onSave,
  onTextUpdate,
  onTextApply,
  onTextDelete,
  onTextCreate,
  onTextMoveStart,
  onTextMove,
  onTextMoveEnd,
  onUndo,
  history,
  historyIndex,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(
    null
  );
  const [dragEnd, setDragEnd] = useState<{ x: number; y: number } | null>(null);
  const [movingTextId, setMovingTextId] = useState<string | null>(null);
  const [moveOffset, setMoveOffset] = useState<{ x: number; y: number } | null>(
    null
  );
  const [justFinishedMoving, setJustFinishedMoving] = useState(false);

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const context = canvas.getContext("2d");
    if (!context) return;

    contextRef.current = context;
    context.lineCap = "round";
    context.lineJoin = "round";

    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => {
      const maxWidth = 800;
      const maxHeight = 600;
      const scale = Math.min(maxWidth / img.width, maxHeight / img.height, 1);

      canvas.width = img.width * scale;
      canvas.height = img.height * scale;

      context.drawImage(img, 0, 0, canvas.width, canvas.height);
      imageRef.current = img;

      onUndo(); // Trigger initial history save
    };
    img.onerror = () => console.error("Failed to load image:", imageUrl);
    img.src = imageUrl;
  }, [imageUrl, onUndo]);

  // Redraw canvas
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const context = contextRef.current;
    if (!canvas || !context || !imageRef.current) return;

    context.clearRect(0, 0, canvas.width, canvas.height);
    context.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);

    console.log("redrawCanvas called, textElements:", textElements.length);
    textElements.forEach((textEl, i) => {
      console.log(`Text element ${i}:`, {
        text: textEl.text,
        x: textEl.x,
        y: textEl.y,
        isEditing: textEl.isEditing,
        isFinalized: textEl.isFinalized,
        fontSize: textEl.fontSize,
        color: textEl.color,
      });

      // Only draw text that is finalized AND not being edited
      if (!textEl.isEditing && textEl.isFinalized) {
        console.log("Drawing text element:", textEl.text);
        context.font = `${textEl.fontSize}px Arial`;
        context.fillStyle = textEl.color;

        // Handle multi-line text
        const lines = textEl.text.split("\n");
        lines.forEach((line, index) => {
          const lineY = textEl.y + index * textEl.fontSize * 1.2; // 1.2 line height
          console.log(`Drawing line "${line}" at (${textEl.x}, ${lineY})`);
          context.fillText(line, textEl.x, lineY);
        });
      } else {
        console.log(
          "Skipping text element:",
          textEl.text,
          "isEditing:",
          textEl.isEditing,
          "isFinalized:",
          textEl.isFinalized
        );
      }
    });
  }, [textElements]);

  // Save to history
  const saveToHistory = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const dataURL = canvas.toDataURL("image/png", 0.8);
    onUndo();
  }, [onUndo]);

  // Handle shift key release to stop text movement
  useEffect(() => {
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Shift" && movingTextId) {
        // Stop text movement when Shift is released
        setMovingTextId(null);
        setMoveOffset(null);
        setJustFinishedMoving(true);
        setTimeout(() => setJustFinishedMoving(false), 100);
        saveToHistory();
      }
    };

    window.addEventListener("keyup", handleKeyUp);
    return () => {
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [movingTextId, saveToHistory]);

  // Undo action
  useEffect(() => {
    if (historyIndex <= 0) return;

    const canvas = canvasRef.current;
    const context = contextRef.current;
    if (!canvas || !context) return;

    const prevState = history[historyIndex - 1];
    const img = new Image();
    img.onload = () => {
      context.clearRect(0, 0, canvas.width, canvas.height);
      context.drawImage(img, 0, 0);
      redrawCanvas();
    };
    img.src = prevState;
  }, [history, historyIndex, redrawCanvas]);

  // Get canvas coordinates
  const getCanvasPos = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: ((e.clientX - rect.left) / rect.width) * canvas.width,
      y: ((e.clientY - rect.top) / rect.height) * canvas.height,
    };
  };

  // Get overlay coordinates (for positioning overlays)
  const getOverlayPos = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const canvasRect = canvas.getBoundingClientRect();
    const containerRect = canvas.parentElement?.getBoundingClientRect();
    if (!containerRect) return { x: 0, y: 0 };

    return {
      x: e.clientX - containerRect.left,
      y: e.clientY - containerRect.top,
    };
  };

  // Handle mouse down
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const pos = getCanvasPos(e);
    const overlayPos = getOverlayPos(e);

    if (tool === "brush") {
      setIsDrawing(true);
      const context = contextRef.current;
      if (!context) return;

      context.beginPath();
      context.moveTo(pos.x, pos.y);
      context.strokeStyle = brushColor;
      context.lineWidth = brushSize;
    } else if (tool === "text") {
      // Only detect clicks on finalized text (not currently being edited)
      const clickedText = textElements.find((textEl) => {
        if (!textEl.isFinalized || textEl.isEditing) return false;
        const textWidth = textEl.width;
        const textHeight = textEl.height;
        return (
          pos.x >= textEl.x &&
          pos.x <= textEl.x + textWidth &&
          pos.y >= textEl.y - textHeight &&
          pos.y <= textEl.y
        );
      });

      if (clickedText && (e.shiftKey || isShiftPressed)) {
        // Allow moving text even if it's currently being edited
        setMovingTextId(clickedText.id);
        setMoveOffset({
          x: pos.x - clickedText.x,
          y: pos.y - clickedText.y,
        });
        onTextMoveStart(clickedText.id, pos);
      } else if (clickedText && !justFinishedMoving && !clickedText.isEditing) {
        // Only start editing if not already editing
        onTextUpdate(clickedText.id, { isEditing: true, isFinalized: false });
        // Force immediate redraw to ensure text is hidden from canvas
        setTimeout(() => {
          redrawCanvas();
        }, 0);
      } else if (!movingTextId) {
        // Only start drag selection if not currently moving text
        setIsDragging(true);
        setDragStart(overlayPos);
        setDragEnd(overlayPos);
        textElements.forEach((el) => onTextUpdate(el.id, { isEditing: false }));
      }
    }
  };

  // Handle mouse move
  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const pos = getCanvasPos(e);
    const overlayPos = getOverlayPos(e);

    if (tool === "brush" && isDrawing) {
      const context = contextRef.current;
      if (!context) return;

      context.lineTo(pos.x, pos.y);
      context.stroke();
    } else if (tool === "text") {
      if (isDragging) {
        setDragEnd(overlayPos);
      } else if (movingTextId && moveOffset) {
        const newX = pos.x - moveOffset.x;
        const newY = pos.y - moveOffset.y;
        onTextMove(movingTextId, newX, newY);
        redrawCanvas();
      }
    }
  };

  // Handle mouse up
  const stopDrawing = () => {
    if (isDrawing) {
      setIsDrawing(false);
      saveToHistory();
    } else if (isDragging && dragStart && dragEnd && !movingTextId) {
      // Convert overlay coordinates back to canvas coordinates for text creation
      const canvas = canvasRef.current;
      if (!canvas) return;

      const canvasRect = canvas.getBoundingClientRect();
      const containerRect = canvas.parentElement?.getBoundingClientRect();
      if (!containerRect) return;

      const startCanvasX =
        ((dragStart.x - (canvasRect.left - containerRect.left)) /
          canvasRect.width) *
        canvas.width;
      const startCanvasY =
        ((dragStart.y - (canvasRect.top - containerRect.top)) /
          canvasRect.height) *
        canvas.height;
      const endCanvasX =
        ((dragEnd.x - (canvasRect.left - containerRect.left)) /
          canvasRect.width) *
        canvas.width;
      const endCanvasY =
        ((dragEnd.y - (canvasRect.top - containerRect.top)) /
          canvasRect.height) *
        canvas.height;

      const x = Math.min(startCanvasX, endCanvasX);
      const topY = Math.min(startCanvasY, endCanvasY);
      const width = Math.abs(endCanvasX - startCanvasX);
      const height = Math.abs(endCanvasY - startCanvasY);

      if (width > 20 && height > 20) {
        // Set y to be the baseline position (top + height)
        // This way the positioning calculation (y - height) gives us the top
        const y = topY + height;

        const newTextElement = {
          id: Date.now().toString(),
          x,
          y,
          text: "",
          color: brushColor,
          fontSize: Math.min(Math.max(height / 2, 12), 48),
          isEditing: true,
          width,
          height,
          isFinalized: false,
        };

        onTextCreate(newTextElement);

        // Force a redraw to ensure the canvas is clean (no text drawn)
        setTimeout(() => {
          redrawCanvas();
        }, 0);
      }
      setIsDragging(false);
      setDragStart(null);
      setDragEnd(null);
    } else if (movingTextId) {
      onTextMoveEnd();
      setMovingTextId(null);
      setMoveOffset(null);
      setJustFinishedMoving(true);
      // Clear the flag after a short delay to prevent immediate edit mode
      setTimeout(() => setJustFinishedMoving(false), 100);
      saveToHistory();
    }
  };

  // Save image
  const saveImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    redrawCanvas();
    const dataURL = canvas.toDataURL("image/png", 0.8);
    onSave(dataURL);
  };

  return (
    <div className="flex-1 p-2 md:p-4 flex items-center justify-center bg-gray-100 min-h-[300px] md:min-h-[500px] relative">
      <canvas
        ref={canvasRef}
        width={800}
        height={600}
        className="border border-gray-300 bg-white shadow-lg max-w-full max-h-full cursor-crosshair"
        onMouseDown={startDrawing}
        onMouseMove={draw}
        onMouseUp={stopDrawing}
        onMouseLeave={() => {
          if (tool === "brush" && isDrawing) stopDrawing();
        }}
        onDragStart={(e) => e.preventDefault()} // Prevent image drag
        style={{
          touchAction: "none",
          userSelect: "none", // Prevent selection
          WebkitUserSelect: "none", // Safari
          MozUserSelect: "none", // Firefox
        }}
      />
      {isDragging && dragStart && dragEnd && tool === "text" && (
        <div
          className="absolute border-2 border-dashed border-blue-500 pointer-events-none"
          style={{
            left:
              (Math.min(dragStart.x, dragEnd.x) / canvasRef.current!.width) *
              canvasRef.current!.getBoundingClientRect().width,
            top:
              (Math.min(dragStart.y, dragEnd.y) / canvasRef.current!.height) *
              canvasRef.current!.getBoundingClientRect().height,
            width:
              Math.abs(dragEnd.x - dragStart.x) *
              (canvasRef.current!.width /
                canvasRef.current!.getBoundingClientRect().width),
            height:
              Math.abs(dragEnd.y - dragStart.y) *
              (canvasRef.current!.height /
                canvasRef.current!.getBoundingClientRect().height),
            zIndex: 5,
            backgroundColor: "transparent",
          }}
        />
      )}
      {textElements.map((textEl) => (
        <TextIndicator
          key={`indicator-${textEl.id}`}
          textEl={textEl}
          isShiftPressed={isShiftPressed}
          movingTextId={movingTextId}
          canvasRef={canvasRef}
          justFinishedMoving={justFinishedMoving}
          onStartMove={(id, pos) => {
            setMovingTextId(id);
            setMoveOffset({ x: pos.x - textEl.x, y: pos.y - textEl.y });
            onTextMoveStart(id, pos);
          }}
          onEdit={(id) => {
            onTextUpdate(id, { isEditing: true, isFinalized: false });
            redrawCanvas();
          }}
        />
      ))}
      {textElements.map((textEl) => (
        <InlineTextEditor
          key={textEl.id}
          textEl={textEl}
          canvasRef={canvasRef}
          onUpdate={onTextUpdate}
          onApply={onTextApply}
          onDelete={onTextDelete}
        />
      ))}
    </div>
  );
};

// Main ImageEditor Component
const ImageEditor: React.FC<ImageEditorProps> = ({
  imageUrl,
  onSave,
  onClose,
}) => {
  const [tool, setTool] = useState<"brush" | "text">("brush");
  const [brushColor, setBrushColor] = useState("#ff0000");
  const [brushSize, setBrushSize] = useState(5);
  const [textElements, setTextElements] = useState<TextElement[]>([]);
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isShiftPressed, setIsShiftPressed] = useState(false);

  // Track shift key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Shift") setIsShiftPressed(true);
    };
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Shift") setIsShiftPressed(false);
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  // Handle undo
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex((prev) => prev - 1);
    }
  }, [historyIndex]);

  return (
    <div className="fixed inset-0 bg-white/20 backdrop-blur-md flex items-center justify-center z-50 p-2 md:p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-3 md:p-4 border-b">
          <h2 className="text-lg md:text-xl font-bold">Image Editor</h2>
          <div className="flex space-x-1 md:space-x-2">
            <button
              onClick={() => {
                const canvas = document.querySelector("canvas");
                if (canvas) {
                  const dataURL = canvas.toDataURL("image/png", 0.8);
                  onSave(dataURL);
                }
              }}
              className="bg-green-600 hover:bg-green-700 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-1 md:space-x-2 text-sm md:text-base"
            >
              <Download size={14} className="md:w-4 md:h-4" />
              <span className="hidden sm:inline">Save</span>
            </button>
            <button
              onClick={onClose}
              className="bg-gray-600 hover:bg-gray-700 text-white px-3 md:px-4 py-2 rounded-lg text-sm md:text-base"
            >
              <span className="hidden sm:inline">Cancel</span>
              <span className="sm:hidden">✕</span>
            </button>
          </div>
        </div>
        <div className="flex flex-col md:flex-row">
          <EditorToolbar
            tool={tool}
            setTool={setTool}
            brushColor={brushColor}
            setBrushColor={setBrushColor}
            brushSize={brushSize}
            setBrushSize={setBrushSize}
            isShiftPressed={isShiftPressed}
            onUndo={handleUndo}
            canUndo={historyIndex > 0}
            textElements={textElements}
            onTextColorChange={(color) => {
              // Apply color to currently editing text
              setTextElements((prev) =>
                prev.map((el) => (el.isEditing ? { ...el, color: color } : el))
              );
            }}
          />
          <EditorCanvas
            imageUrl={imageUrl}
            tool={tool}
            brushColor={brushColor}
            brushSize={brushSize}
            textElements={textElements}
            isShiftPressed={isShiftPressed}
            onSave={onSave}
            onUndo={() => {
              if (historyIndex > 0) {
                setHistoryIndex((prev) => prev - 1);
                const canvas = document.querySelector("canvas");
                if (canvas) {
                  const context = canvas.getContext("2d");
                  const img = new Image();
                  img.onload = () => {
                    if (context) {
                      context.clearRect(0, 0, canvas.width, canvas.height);
                      context.drawImage(img, 0, 0);
                    }
                  };
                  img.src = history[historyIndex - 1];
                }
              }
            }}
            onTextUpdate={(id, updates) =>
              setTextElements((prev) =>
                prev.map((el) => {
                  if (el.id === id) {
                    const updatedEl = { ...el, ...updates };

                    // If text content is being updated, resize the text box
                    if (
                      updates.text !== undefined ||
                      updates.fontSize !== undefined
                    ) {
                      const canvas = document.querySelector("canvas");
                      const context = canvas?.getContext("2d");
                      if (canvas && context) {
                        context.font = `${updatedEl.fontSize}px Arial`;

                        // Handle multi-line text for width/height calculation
                        const lines = (updatedEl.text || "").split("\n");
                        let maxWidth = 0;

                        // Find the widest line
                        lines.forEach((line) => {
                          const textMetrics = context.measureText(line);
                          maxWidth = Math.max(maxWidth, textMetrics.width);
                        });

                        const textWidth = maxWidth;
                        const textHeight =
                          updatedEl.fontSize * lines.length * 1.2; // Account for line height
                        const padding = 8;

                        updatedEl.width = Math.max(textWidth + padding * 2, 50);
                        updatedEl.height = Math.max(
                          textHeight + padding * 2,
                          30
                        );
                      }
                    }

                    return updatedEl;
                  }
                  return el;
                })
              )
            }
            onTextApply={(id) => {
              const textEl = textElements.find((el) => el.id === id);
              if (!textEl) return;

              // Check if text is empty after trimming whitespace
              if (!textEl.text.trim()) {
                // Remove empty text element instead of applying it
                setTextElements((prev) => prev.filter((el) => el.id !== id));
                return;
              }

              // Calculate the top-left position of the text box
              const topY = textEl.y - textEl.height;

              // Position text at top-left of the box (with small padding)
              const textX = textEl.x + 8; // 8px padding from left
              const textY = topY + textEl.fontSize + 8; // Position at baseline with padding from top

              // Measure text to resize the text box (using a temporary canvas for measurement)
              const tempCanvas = document.createElement("canvas");
              const tempContext = tempCanvas.getContext("2d");
              if (tempContext) {
                tempContext.font = `${textEl.fontSize}px Arial`;

                // Handle multi-line text for width calculation
                const lines = textEl.text.split("\n");
                let maxWidth = 0;
                lines.forEach((line) => {
                  const textMetrics = tempContext.measureText(line);
                  maxWidth = Math.max(maxWidth, textMetrics.width);
                });

                const textWidth = maxWidth;
                const textHeight = textEl.fontSize * lines.length * 1.2; // Account for line height
                const padding = 8;

                const newWidth = Math.max(textWidth + padding * 2, 50);
                const newHeight = Math.max(textHeight + padding * 2, 30);

                // Update text element state - let redrawCanvas handle the actual drawing
                setTextElements((prev) =>
                  prev.map((el) =>
                    el.id === id
                      ? {
                          ...el,
                          x: textX, // Update to actual text position
                          y: textY, // Update to actual text baseline
                          isEditing: false,
                          isFinalized: true,
                          width: newWidth,
                          height: newHeight,
                        }
                      : el
                  )
                );

                // Save to history after state update
                setTimeout(() => {
                  const canvas = document.querySelector("canvas");
                  if (canvas) {
                    setHistory((prev) => {
                      const newHistory = prev.slice(0, historyIndex + 1);
                      newHistory.push(canvas.toDataURL("image/png", 0.8));
                      return newHistory.slice(-20);
                    });
                    setHistoryIndex((prev) => prev + 1);
                  }
                }, 0);
              }
            }}
            onTextDelete={(id) =>
              setTextElements((prev) => prev.filter((el) => el.id !== id))
            }
            onTextCreate={(text) => setTextElements((prev) => [...prev, text])}
            onTextMoveStart={(id, pos) => {
              setTextElements((prev) =>
                prev.map((el) => ({ ...el, isEditing: false }))
              );
            }}
            onTextMove={(id, x, y) =>
              setTextElements((prev) =>
                prev.map((el) => (el.id === id ? { ...el, x, y } : el))
              )
            }
            onTextMoveEnd={() => {
              const canvas = document.querySelector("canvas");
              if (canvas) {
                setHistory((prev) => {
                  const newHistory = prev.slice(0, historyIndex + 1);
                  newHistory.push(canvas.toDataURL("image/png", 0.8));
                  return newHistory.slice(-20);
                });
                setHistoryIndex((prev) => prev + 1);
              }
            }}
            history={history}
            historyIndex={historyIndex}
          />
        </div>
      </div>
    </div>
  );
};

export default ImageEditor;
