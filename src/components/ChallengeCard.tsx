import { Calendar, Users, Trophy } from "lucide-react";
import type { Challenge } from "@/data/mockData";

interface ChallengeCardProps {
  challenge: Challenge;
  onJoinChallenge?: (challenge: Challenge) => void;
}

const ChallengeCard = ({ challenge, onJoinChallenge }: ChallengeCardProps) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getDaysLeft = () => {
    const now = new Date();
    const endDate = new Date(challenge.endDate);
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const daysLeft = getDaysLeft();

  return (
    <div className="bg-gradient-to-br from-purple-600 to-pink-600 text-white rounded-lg p-6">
      <div className="flex items-center space-x-2 mb-3">
        <Trophy size={20} className="text-yellow-300" />
        <span className="text-sm font-medium uppercase tracking-wide">
          Weekly Challenge
        </span>
      </div>

      <h3 className="text-xl font-bold mb-3">{challenge.title}</h3>

      <p className="text-purple-100 mb-4 text-sm leading-relaxed">
        {challenge.description}
      </p>

      {/* Challenge Image Preview */}
      {challenge.type === "image" && (
        <div className="mb-4">
          <img
            src={challenge.baseContent}
            alt="Challenge base content"
            className="w-full h-32 object-cover rounded-lg border-2 border-white/20"
            onError={(e) => {
              e.currentTarget.src =
                "https://via.placeholder.com/300x200?text=Challenge+Image";
            }}
          />
        </div>
      )}

      {/* Challenge Stats */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Users size={16} />
            <span className="text-sm">{challenge.participantCount} joined</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar size={16} />
            <span className="text-sm">
              {daysLeft > 0 ? `${daysLeft} days left` : "Ended"}
            </span>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <button
        onClick={() => onJoinChallenge?.(challenge)}
        className="w-full bg-white text-purple-600 font-semibold py-3 px-4 rounded-lg hover:bg-purple-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={daysLeft === 0}
      >
        {daysLeft > 0 ? "Join Challenge" : "Challenge Ended"}
      </button>

      <p className="text-xs text-purple-200 mt-2 text-center">
        Ends {formatDate(challenge.endDate)}
      </p>
    </div>
  );
};

export default ChallengeCard;
