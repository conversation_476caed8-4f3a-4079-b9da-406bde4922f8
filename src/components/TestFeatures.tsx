import { useState } from 'react';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
}

const TestFeatures = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    // Test 1: Check if mock data loads
    try {
      const { mockPosts } = await import('../data/mockData');
      if (mockPosts.length > 0) {
        results.push({
          name: 'Mock Data Loading',
          status: 'pass',
          message: `Loaded ${mockPosts.length} sample posts`
        });
      } else {
        results.push({
          name: 'Mock Data Loading',
          status: 'fail',
          message: 'No mock posts found'
        });
      }
    } catch (error) {
      results.push({
        name: 'Mock Data Loading',
        status: 'fail',
        message: 'Failed to load mock data'
      });
    }

    // Test 2: Check if local storage works
    try {
      localStorage.setItem('test', 'value');
      const value = localStorage.getItem('test');
      localStorage.removeItem('test');
      
      if (value === 'value') {
        results.push({
          name: 'Local Storage',
          status: 'pass',
          message: 'Local storage is working'
        });
      } else {
        results.push({
          name: 'Local Storage',
          status: 'fail',
          message: 'Local storage read/write failed'
        });
      }
    } catch (error) {
      results.push({
        name: 'Local Storage',
        status: 'fail',
        message: 'Local storage not available'
      });
    }

    // Test 3: Check if file validation works
    try {
      const { validateTextContent } = await import('../utils/fileValidation');
      const result = validateTextContent('Test text');
      
      if (result.isValid) {
        results.push({
          name: 'File Validation',
          status: 'pass',
          message: 'Text validation working'
        });
      } else {
        results.push({
          name: 'File Validation',
          status: 'fail',
          message: 'Text validation failed'
        });
      }
    } catch (error) {
      results.push({
        name: 'File Validation',
        status: 'fail',
        message: 'File validation module error'
      });
    }

    // Test 4: Check if Fabric.js is available
    try {
      const fabric = await import('fabric');
      if (fabric.fabric) {
        results.push({
          name: 'Fabric.js Library',
          status: 'pass',
          message: 'Fabric.js loaded successfully'
        });
      } else {
        results.push({
          name: 'Fabric.js Library',
          status: 'fail',
          message: 'Fabric.js not properly loaded'
        });
      }
    } catch (error) {
      results.push({
        name: 'Fabric.js Library',
        status: 'fail',
        message: 'Fabric.js import failed'
      });
    }

    // Test 5: Check if Web Audio API is available
    try {
      if (window.AudioContext || (window as any).webkitAudioContext) {
        results.push({
          name: 'Web Audio API',
          status: 'pass',
          message: 'Web Audio API is available'
        });
      } else {
        results.push({
          name: 'Web Audio API',
          status: 'warning',
          message: 'Web Audio API not supported in this browser'
        });
      }
    } catch (error) {
      results.push({
        name: 'Web Audio API',
        status: 'fail',
        message: 'Error checking Web Audio API'
      });
    }

    // Test 6: Check if MediaDevices API is available (for recording)
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        results.push({
          name: 'Media Recording',
          status: 'pass',
          message: 'Media recording API available'
        });
      } else {
        results.push({
          name: 'Media Recording',
          status: 'warning',
          message: 'Media recording not supported or requires HTTPS'
        });
      }
    } catch (error) {
      results.push({
        name: 'Media Recording',
        status: 'fail',
        message: 'Error checking media recording API'
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'fail':
        return <XCircle className="text-red-500" size={20} />;
      case 'warning':
        return <AlertCircle className="text-yellow-500" size={20} />;
    }
  };

  const passCount = testResults.filter(r => r.status === 'pass').length;
  const failCount = testResults.filter(r => r.status === 'fail').length;
  const warningCount = testResults.filter(r => r.status === 'warning').length;

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Feature Tests</h3>
        <button
          onClick={runTests}
          disabled={isRunning}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg"
        >
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </button>
      </div>

      {testResults.length > 0 && (
        <>
          <div className="mb-4 flex space-x-4 text-sm">
            <span className="text-green-600">✓ {passCount} Passed</span>
            <span className="text-red-600">✗ {failCount} Failed</span>
            <span className="text-yellow-600">⚠ {warningCount} Warnings</span>
          </div>

          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div key={index} className="flex items-center space-x-3 p-2 rounded border">
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium">{result.name}</div>
                  <div className="text-sm text-gray-600">{result.message}</div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {testResults.length === 0 && !isRunning && (
        <p className="text-gray-500 text-center py-4">
          Click "Run Tests" to verify all features are working correctly.
        </p>
      )}
    </div>
  );
};

export default TestFeatures;
