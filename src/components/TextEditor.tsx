import { useState } from "react";
import {
  Type,
  Palette,
  Sparkles,
  Download,
  X,
  Music,
  Play,
  Pause,
} from "lucide-react";
import { validateTextContent } from "../utils/fileValidation";
import { sampleAudioTracks } from "../data/mockData";

interface TextEditorProps {
  originalText: string;
  onSave: (editedText: string, effects: string[], audioTrack?: string) => void;
  onClose: () => void;
}

const TextEditor = ({ originalText, onSave, onClose }: TextEditorProps) => {
  const [text, setText] = useState(originalText);
  const [selectedEffects, setSelectedEffects] = useState<string[]>([]);
  const [fontSize, setFontSize] = useState(18);
  const [textAlign, setTextAlign] = useState<"left" | "center" | "right">(
    "left"
  );
  const [selectedAudioTrack, setSelectedAudioTrack] = useState<string | null>(
    null
  );
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);

  // Effect customization states
  const [glowColor, setGlowColor] = useState("#8b5cf6");
  const [glowIntensity, setGlowIntensity] = useState(30);
  const [shakeIntensity, setShakeIntensity] = useState(3);
  const [waveHeight, setWaveHeight] = useState(15);
  const [waveSpeed, setWaveSpeed] = useState(2);

  const effects = [
    {
      id: "rainbow_sweep",
      name: "Rainbow Sweep",
      description: "Animated rainbow gradient sweep",
    },
    {
      id: "rainbow_text",
      name: "Rainbow",
      description: "All characters change rainbow colors",
    },
    { id: "shake_animation", name: "Shake", description: "Shaking animation" },
    { id: "wave_text", name: "Wave", description: "Floating wave motion" },
    { id: "glow_text", name: "Glow", description: "Glowing text effect" },
  ];

  const toggleEffect = (effectId: string) => {
    setSelectedEffects((prev) =>
      prev.includes(effectId)
        ? prev.filter((id) => id !== effectId)
        : [...prev, effectId]
    );
  };

  const getTextClasses = () => {
    let classes = "transition-all duration-300 font-bold ";

    if (selectedEffects.includes("rainbow_sweep")) {
      classes += "animate-pulse ";
    }
    if (selectedEffects.includes("rainbow_text")) {
      classes += "animate-pulse ";
    }
    if (selectedEffects.includes("shake_animation")) {
      classes += "animate-bounce ";
    }
    if (selectedEffects.includes("wave_text")) {
      classes += "animate-pulse ";
    }
    if (selectedEffects.includes("glow_text")) {
      classes += "drop-shadow-lg ";
    }

    return classes;
  };

  const getTextStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      fontSize: `${fontSize}px`,
      textAlign,
      lineHeight: 1.4,
      padding: "20px",
      minHeight: "200px",
      wordWrap: "break-word",
      fontWeight: "bold",
    };

    // Apply visual effects
    if (selectedEffects.includes("rainbow_sweep")) {
      baseStyle.background =
        "linear-gradient(45deg, #dc2626, #ea580c, #ca8a04, #16a34a, #2563eb, #7c3aed, #c026d3)";
      baseStyle.backgroundSize = "400% 400%";
      baseStyle.WebkitBackgroundClip = "text";
      baseStyle.WebkitTextFillColor = "transparent";
      baseStyle.backgroundClip = "text";
      baseStyle.animation = "rainbow-sweep 3s ease-in-out infinite";
    }

    if (selectedEffects.includes("rainbow_text")) {
      baseStyle.animation =
        (baseStyle.animation ? baseStyle.animation + ", " : "") +
        "rainbow-colors 2s ease-in-out infinite";
    }

    if (selectedEffects.includes("glow_text")) {
      baseStyle.textShadow = `0 0 ${glowIntensity}px ${glowColor}, 0 0 ${
        glowIntensity * 2
      }px ${glowColor}, 0 0 ${glowIntensity * 3}px ${glowColor}`;
      baseStyle.color = glowColor;
      baseStyle.animation =
        (baseStyle.animation ? baseStyle.animation + ", " : "") +
        "glow-pulse 2s ease-in-out infinite";
    }

    if (selectedEffects.includes("shake_animation")) {
      baseStyle.animation =
        (baseStyle.animation ? baseStyle.animation + ", " : "") +
        "shake 0.5s ease-in-out infinite";
    }

    // If no color effects are applied, use default color
    if (
      !selectedEffects.includes("rainbow_sweep") &&
      !selectedEffects.includes("rainbow_text") &&
      !selectedEffects.includes("glow_text")
    ) {
      baseStyle.color = "#374151"; // gray-700
    }

    return baseStyle;
  };

  const handleSave = () => {
    const validation = validateTextContent(text);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    const effects = [...selectedEffects];
    if (selectedAudioTrack) {
      effects.push("background_audio");
    }

    onSave(text, effects, selectedAudioTrack || undefined);
  };

  const previewText = text || "Your text will appear here...";

  // Component for wave text effect
  const WaveText = ({
    children,
    style,
  }: {
    children: string;
    style: React.CSSProperties;
  }) => {
    if (!selectedEffects.includes("wave_text")) {
      return <span style={style}>{children}</span>;
    }

    // Calculate proper line height to prevent overlap
    const waveLineHeight = Math.max(1.8, (waveHeight * 2 + 20) / fontSize);

    return (
      <span
        style={{
          ...style,
          lineHeight: waveLineHeight,
          display: "inline-block",
          width: "100%",
        }}
      >
        {children.split("").map((char, index) => (
          <span
            key={index}
            style={{
              display: "inline-block",
              animation: `wave ${waveSpeed}s ease-in-out infinite`,
              animationDelay: `${index * 0.1}s`,
              verticalAlign: "baseline",
            }}
          >
            {char === " " ? "\u00A0" : char}
          </span>
        ))}
      </span>
    );
  };

  return (
    <>
      <style>{`
        @keyframes rainbow-sweep {
          0% { background-position: 0% 50%; }
          25% { background-position: 100% 50%; }
          50% { background-position: 100% 100%; }
          75% { background-position: 0% 100%; }
          100% { background-position: 0% 50%; }
        }

        @keyframes rainbow-colors {
          0% { color: #dc2626; }    /* Elegant red */
          14% { color: #ea580c; }   /* Warm orange */
          28% { color: #ca8a04; }   /* Rich golden yellow */
          42% { color: #16a34a; }   /* Forest green */
          57% { color: #2563eb; }   /* Deep blue */
          71% { color: #7c3aed; }   /* Rich purple */
          85% { color: #c026d3; }   /* Vibrant magenta */
          100% { color: #dc2626; }  /* Back to elegant red */
        }

        @keyframes shake {
          0%, 100% { transform: translateX(0) rotate(0deg); }
          10% { transform: translateX(-${shakeIntensity}px) rotate(-1deg); }
          20% { transform: translateX(${shakeIntensity}px) rotate(1deg); }
          30% { transform: translateX(-${shakeIntensity}px) rotate(-1deg); }
          40% { transform: translateX(${shakeIntensity}px) rotate(1deg); }
          50% { transform: translateX(-${
            shakeIntensity * 0.7
          }px) rotate(-0.5deg); }
          60% { transform: translateX(${
            shakeIntensity * 0.7
          }px) rotate(0.5deg); }
          70% { transform: translateX(-${
            shakeIntensity * 0.7
          }px) rotate(-0.5deg); }
          80% { transform: translateX(${
            shakeIntensity * 0.7
          }px) rotate(0.5deg); }
          90% { transform: translateX(-${
            shakeIntensity * 0.3
          }px) rotate(0deg); }
        }

        @keyframes wave {
          0%, 100% { transform: translateY(0px) scale(1); }
          25% { transform: translateY(-${waveHeight * 0.5}px) scale(1.02); }
          50% { transform: translateY(-${waveHeight}px) scale(1.05); }
          75% { transform: translateY(-${waveHeight * 0.5}px) scale(1.02); }
        }

        @keyframes glow-pulse {
          0%, 100% {
            text-shadow: 0 0 ${glowIntensity}px ${glowColor}, 0 0 ${
        glowIntensity * 2
      }px ${glowColor}, 0 0 ${glowIntensity * 3}px ${glowColor};
          }
          50% {
            text-shadow: 0 0 ${glowIntensity * 1.5}px ${glowColor}, 0 0 ${
        glowIntensity * 3
      }px ${glowColor}, 0 0 ${glowIntensity * 4.5}px ${glowColor}, 0 0 ${
        glowIntensity * 6
      }px ${glowColor};
          }
        }
      `}</style>
      <div className="fixed inset-0 bg-white/20 backdrop-blur-md flex items-center justify-center z-50 p-2 md:p-4">
        <div className="bg-white rounded-lg max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-hidden flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 md:p-6 border-b flex-shrink-0">
            <h2 className="text-lg md:text-xl font-bold flex items-center space-x-2">
              <Type size={20} className="md:w-6 md:h-6" />
              <span>Text Remix Editor</span>
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-1 md:space-x-2 text-sm md:text-base"
              >
                <Download size={14} className="md:w-4 md:h-4" />
                <span className="hidden sm:inline">Save Remix</span>
                <span className="sm:hidden">Save</span>
              </button>
              <button
                onClick={onClose}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 md:px-4 py-2 rounded-lg text-sm md:text-base"
              >
                <X size={14} className="md:w-4 md:h-4" />
              </button>
            </div>
          </div>

          <div className="flex flex-col md:flex-row flex-1 min-h-0">
            {/* Editor Panel */}
            <div className="w-full md:w-1/2 p-4 md:p-6 border-b md:border-b-0 md:border-r bg-gray-50 overflow-y-auto">
              <h3 className="font-semibold mb-4 text-lg">Edit Your Text</h3>

              {/* Text Input */}
              <textarea
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="Enter your remixed text here..."
                className="w-full h-32 md:h-40 p-3 md:p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent mb-4 text-sm md:text-base"
                maxLength={500}
              />

              <div className="text-right text-sm text-gray-600 mb-4 md:mb-6">
                {text.length}/500 characters
              </div>

              {/* Text Formatting */}
              <div className="space-y-3 md:space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 md:mb-2">
                    Font Size
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="48"
                    value={fontSize}
                    onChange={(e) => setFontSize(Number(e.target.value))}
                    className="w-full"
                  />
                  <span className="text-sm text-gray-600">{fontSize}px</span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 md:mb-2">
                    Text Alignment
                  </label>
                  <div className="flex space-x-1 md:space-x-2">
                    {(["left", "center", "right"] as const).map((align) => (
                      <button
                        key={align}
                        onClick={() => setTextAlign(align)}
                        className={`px-2 md:px-3 py-1 md:py-2 rounded-lg border text-xs md:text-sm capitalize ${
                          textAlign === align
                            ? "bg-purple-100 border-purple-500 text-purple-700"
                            : "border-gray-300 hover:border-gray-400"
                        }`}
                      >
                        {align}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Effects */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 md:mb-2">
                    <Sparkles size={16} className="inline mr-1" />
                    Text Effects
                  </label>
                  <div className="space-y-1 md:space-y-2 max-h-32 md:max-h-40 overflow-y-auto">
                    {effects.map((effect) => (
                      <label
                        key={effect.id}
                        className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1"
                      >
                        <input
                          type="checkbox"
                          checked={selectedEffects.includes(effect.id)}
                          onChange={() => toggleEffect(effect.id)}
                          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <div>
                          <div className="font-medium text-xs md:text-sm">
                            {effect.name}
                          </div>
                          <div className="text-xs text-gray-600">
                            {effect.description}
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Effect Customization */}
                {selectedEffects.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1 md:mb-2">
                      <Palette size={16} className="inline mr-1" />
                      Effect Settings
                    </label>
                    <div className="space-y-2 md:space-y-3 p-2 bg-gray-100 rounded-lg">
                      {/* Glow Customization */}
                      {selectedEffects.includes("glow_text") && (
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-gray-600">
                            Glow Effect
                          </div>
                          <div className="flex items-center space-x-2">
                            <label className="text-xs text-gray-600 w-12">
                              Color:
                            </label>
                            <input
                              type="color"
                              value={glowColor}
                              onChange={(e) => setGlowColor(e.target.value)}
                              className="w-8 h-6 rounded border border-gray-300"
                            />
                            <span className="text-xs text-gray-500">
                              {glowColor}
                            </span>
                          </div>
                          <div>
                            <label className="text-xs text-gray-600">
                              Intensity: {glowIntensity}px
                            </label>
                            <input
                              type="range"
                              min="10"
                              max="60"
                              value={glowIntensity}
                              onChange={(e) =>
                                setGlowIntensity(Number(e.target.value))
                              }
                              className="w-full"
                            />
                          </div>
                        </div>
                      )}

                      {/* Shake Customization */}
                      {selectedEffects.includes("shake_animation") && (
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-gray-600">
                            Shake Effect
                          </div>
                          <div>
                            <label className="text-xs text-gray-600">
                              Intensity: {shakeIntensity}px
                            </label>
                            <input
                              type="range"
                              min="1"
                              max="10"
                              value={shakeIntensity}
                              onChange={(e) =>
                                setShakeIntensity(Number(e.target.value))
                              }
                              className="w-full"
                            />
                          </div>
                        </div>
                      )}

                      {/* Wave Customization */}
                      {selectedEffects.includes("wave_text") && (
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-gray-600">
                            Wave Effect
                          </div>
                          <div>
                            <label className="text-xs text-gray-600">
                              Height: {waveHeight}px
                            </label>
                            <input
                              type="range"
                              min="5"
                              max="30"
                              value={waveHeight}
                              onChange={(e) =>
                                setWaveHeight(Number(e.target.value))
                              }
                              className="w-full"
                            />
                          </div>
                          <div>
                            <label className="text-xs text-gray-600">
                              Speed: {waveSpeed}s
                            </label>
                            <input
                              type="range"
                              min="0.5"
                              max="5"
                              step="0.5"
                              value={waveSpeed}
                              onChange={(e) =>
                                setWaveSpeed(Number(e.target.value))
                              }
                              className="w-full"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Background Audio */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 md:mb-2">
                    <Music size={16} className="inline mr-1" />
                    Background Audio
                  </label>
                  <div className="space-y-1 md:space-y-2 max-h-32 md:max-h-40 overflow-y-auto">
                    {sampleAudioTracks.map((track) => (
                      <label
                        key={track.id}
                        className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1"
                      >
                        <input
                          type="radio"
                          name="audioTrack"
                          checked={selectedAudioTrack === track.url}
                          onChange={() => setSelectedAudioTrack(track.url)}
                          className="text-purple-600 focus:ring-purple-500"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-xs md:text-sm">
                            {track.name}
                          </div>
                          <div className="text-xs text-gray-600">
                            {track.duration}s
                          </div>
                        </div>
                      </label>
                    ))}
                    <label className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1">
                      <input
                        type="radio"
                        name="audioTrack"
                        checked={selectedAudioTrack === null}
                        onChange={() => setSelectedAudioTrack(null)}
                        className="text-purple-600 focus:ring-purple-500"
                      />
                      <div>
                        <div className="font-medium text-sm">No Audio</div>
                        <div className="text-xs text-gray-600">Text only</div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Preview Panel */}
            <div className="w-full md:w-1/2 p-4 md:p-6 overflow-y-auto">
              <h3 className="font-semibold mb-4 text-lg">Live Preview</h3>

              <div className="border-2 border-dashed border-gray-200 rounded-lg min-h-[200px] md:min-h-[300px] flex items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50 p-4">
                <WaveText style={getTextStyle()}>{previewText}</WaveText>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-3 md:p-4 border-t bg-gray-50 flex-shrink-0">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between text-xs md:text-sm text-gray-600 space-y-2 md:space-y-0">
              <div className="truncate">
                Original: "{originalText.substring(0, 30)}
                {originalText.length > 30 ? "..." : ""}"
              </div>
              <div>
                {selectedEffects.length > 0 && (
                  <span className="text-purple-600 font-medium">
                    {selectedEffects.length} effect
                    {selectedEffects.length !== 1 ? "s" : ""} applied
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TextEditor;
