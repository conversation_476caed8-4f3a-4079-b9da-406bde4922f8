import { useState, useRef, useEffect } from "react";
import {
  Play,
  Pause,
  Square,
  Mic,
  Music,
  Download,
  X,
  Volume2,
} from "lucide-react";
import { sampleAudioTracks } from "../data/mockData";

interface AudioEditorProps {
  originalAudioUrl?: string;
  onSave: (remixedAudioData: string, effects: string[]) => void;
  onClose: () => void;
}

const AudioEditor = ({
  originalAudioUrl,
  onSave,
  onClose,
}: AudioEditorProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedBackgroundTrack, setSelectedBackgroundTrack] = useState<
    string | null
  >(null);
  const [backgroundVolume, setBackgroundVolume] = useState(0.3);
  const [originalVolume, setOriginalVolume] = useState(0.7);
  const [recordedAudio, setRecordedAudio] = useState<string | null>(null);
  const [effects, setEffects] = useState<string[]>([]);

  const audioRef = useRef<HTMLAudioElement>(null);
  const backgroundAudioRef = useRef<HTMLAudioElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    // Initialize audio elements
    if (originalAudioUrl && audioRef.current) {
      audioRef.current.src = originalAudioUrl;
      audioRef.current.volume = originalVolume;
    }
  }, [originalAudioUrl, originalVolume]);

  useEffect(() => {
    if (selectedBackgroundTrack && backgroundAudioRef.current) {
      backgroundAudioRef.current.src = selectedBackgroundTrack;
      backgroundAudioRef.current.volume = backgroundVolume;
      backgroundAudioRef.current.loop = true;
    }
  }, [selectedBackgroundTrack, backgroundVolume]);

  const togglePlayback = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      if (backgroundAudioRef.current) {
        backgroundAudioRef.current.pause();
      }
    } else {
      audioRef.current.play();
      if (selectedBackgroundTrack && backgroundAudioRef.current) {
        backgroundAudioRef.current.play();
      }
    }
    setIsPlaying(!isPlaying);
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);

      mediaRecorderRef.current = mediaRecorder;
      recordedChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, { type: "audio/wav" });
        const url = URL.createObjectURL(blob);
        setRecordedAudio(url);

        // Stop all tracks
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error("Error starting recording:", error);
      alert("Could not access microphone. Please check permissions.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleSave = () => {
    const appliedEffects = [];

    if (selectedBackgroundTrack) {
      appliedEffects.push("background_music");
    }

    if (recordedAudio) {
      appliedEffects.push("voiceover");
    }

    // In a real app, we would mix the audio tracks here
    // For now, we'll just save the original with metadata about the remix
    const audioToSave = recordedAudio || originalAudioUrl || "";

    onSave(audioToSave, appliedEffects);
  };

  return (
    <div className="fixed inset-0 bg-white/20 backdrop-blur-md flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold flex items-center space-x-2">
            <Music size={24} />
            <span>Audio Remix Studio</span>
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <Download size={16} />
              <span>Save Remix</span>
            </button>
            <button
              onClick={onClose}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Original Audio Controls */}
          {originalAudioUrl && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3">Original Audio</h3>
              <div className="flex items-center space-x-4">
                <button
                  onClick={togglePlayback}
                  className="w-12 h-12 bg-purple-600 hover:bg-purple-700 text-white rounded-full flex items-center justify-center"
                >
                  {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                </button>

                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <Volume2 size={16} />
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={originalVolume}
                      onChange={(e) =>
                        setOriginalVolume(Number(e.target.value))
                      }
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-600 w-12">
                      {Math.round(originalVolume * 100)}%
                    </span>
                  </div>
                </div>
              </div>
              <audio ref={audioRef} />
            </div>
          )}

          {/* Background Music */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">Background Music</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
              {sampleAudioTracks.map((track) => (
                <button
                  key={track.id}
                  onClick={() => setSelectedBackgroundTrack(track.url)}
                  className={`p-3 rounded-lg border text-left transition-colors ${
                    selectedBackgroundTrack === track.url
                      ? "bg-blue-100 border-blue-500 text-blue-700"
                      : "bg-white border-gray-300 hover:border-gray-400"
                  }`}
                >
                  <div className="font-medium">{track.name}</div>
                  <div className="text-sm text-gray-600">{track.duration}s</div>
                </button>
              ))}
            </div>

            {selectedBackgroundTrack && (
              <div className="flex items-center space-x-2">
                <Volume2 size={16} />
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={backgroundVolume}
                  onChange={(e) => setBackgroundVolume(Number(e.target.value))}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-12">
                  {Math.round(backgroundVolume * 100)}%
                </span>
              </div>
            )}
            <audio ref={backgroundAudioRef} />
          </div>

          {/* Voice Recording */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">Voice Recording</h3>
            <div className="flex items-center space-x-4">
              <button
                onClick={isRecording ? stopRecording : startRecording}
                className={`w-12 h-12 rounded-full flex items-center justify-center text-white ${
                  isRecording
                    ? "bg-red-600 hover:bg-red-700 animate-pulse"
                    : "bg-green-600 hover:bg-green-700"
                }`}
              >
                {isRecording ? <Square size={20} /> : <Mic size={20} />}
              </button>

              <div className="flex-1">
                {isRecording ? (
                  <div className="text-red-600 font-medium">
                    Recording... Click stop when done
                  </div>
                ) : recordedAudio ? (
                  <div className="text-green-600 font-medium">
                    ✓ Voice recording ready
                  </div>
                ) : (
                  <div className="text-gray-600">
                    Click to start recording your voice
                  </div>
                )}
              </div>
            </div>

            {recordedAudio && (
              <div className="mt-3">
                <audio controls src={recordedAudio} className="w-full" />
              </div>
            )}
          </div>

          {/* Remix Summary */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">Remix Summary</h3>
            <div className="space-y-2 text-sm">
              {originalAudioUrl && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span>Original audio track</span>
                </div>
              )}
              {selectedBackgroundTrack && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Background music added</span>
                </div>
              )}
              {recordedAudio && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Voice recording added</span>
                </div>
              )}
              {!selectedBackgroundTrack && !recordedAudio && (
                <div className="text-gray-600 italic">
                  No effects applied yet
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioEditor;
