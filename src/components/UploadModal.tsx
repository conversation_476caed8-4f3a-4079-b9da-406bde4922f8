import { useState } from "react";
import { X, Upload, Image, Type, Music } from "lucide-react";
import { useDropzone } from "react-dropzone";
import {
  validateImageFile,
  validateAudioFile,
  validateTextContent,
} from "../utils/fileValidation";
import { uploadFile, savePost, checkForDuplicates } from "../services/mockApi";

interface UploadModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

type UploadType = "image" | "text" | "audio";

const UploadModal = ({ onClose, onSuccess }: UploadModalProps) => {
  const [uploadType, setUploadType] = useState<UploadType>("image");
  const [textContent, setTextContent] = useState("");
  const [title, setTitle] = useState("");
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const onDrop = (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setError("");

    if (uploadType === "image") {
      const validation = validateImageFile(file);
      if (!validation.isValid) {
        setError(validation.error || "Invalid file");
        return;
      }
    } else if (uploadType === "audio") {
      const validation = validateAudioFile(file);
      if (!validation.isValid) {
        setError(validation.error || "Invalid file");
        return;
      }
    }

    setSelectedFile(file);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept:
      uploadType === "image"
        ? { "image/*": [".jpeg", ".jpg", ".png", ".gif"] }
        : uploadType === "audio"
        ? { "audio/*": [".mp3", ".wav"] }
        : {},
    multiple: false,
    disabled: uploadType === "text",
  });

  const handleSubmit = async () => {
    if (!title.trim()) {
      setError("Please enter a title");
      return;
    }

    setUploading(true);
    setError("");

    try {
      let content = "";

      if (uploadType === "text") {
        const validation = validateTextContent(textContent);
        if (!validation.isValid) {
          setError(validation.error || "Invalid text");
          return;
        }
        content = textContent;
      } else if (selectedFile) {
        content = await uploadFile(selectedFile);
      } else {
        setError("Please select a file");
        return;
      }

      // Check for duplicates
      const isDuplicate = await checkForDuplicates(content, uploadType);
      if (isDuplicate) {
        setError("Too similar to existing content—add more edits!");
        return;
      }

      // Save the post
      await savePost({
        type: uploadType,
        content,
        title: title.trim(),
        author: "Anonymous", // Since no auth for MVP
        remixCount: 0,
        isRemix: false,
      });

      onSuccess();
    } catch (err) {
      setError("Failed to upload. Please try again.");
      console.error("Upload error:", err);
    } finally {
      setUploading(false);
    }
  };

  const renderUploadArea = () => {
    if (uploadType === "text") {
      return (
        <div className="space-y-4">
          <textarea
            value={textContent}
            onChange={(e) => setTextContent(e.target.value)}
            placeholder="Enter your text content here..."
            className="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            maxLength={500}
          />
          <div className="text-right text-sm text-gray-500">
            {textContent.length}/500 characters
          </div>
        </div>
      );
    }

    return (
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? "border-purple-500 bg-purple-50"
            : "border-gray-300 hover:border-purple-400"
        }`}
      >
        <input {...getInputProps()} />

        {selectedFile ? (
          <div className="space-y-2">
            <div className="text-green-600 font-medium">
              ✓ {selectedFile.name}
            </div>
            <div className="text-sm text-gray-500">
              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setSelectedFile(null);
              }}
              className="text-sm text-purple-600 hover:text-purple-700"
            >
              Choose different file
            </button>
          </div>
        ) : (
          <div className="space-y-2">
            <Upload size={48} className="mx-auto text-gray-400" />
            <div className="text-lg font-medium text-gray-700">
              {isDragActive ? "Drop your file here" : `Upload ${uploadType}`}
            </div>
            <div className="text-sm text-gray-500">
              {uploadType === "image"
                ? "JPEG, PNG, or GIF up to 5MB"
                : "MP3 or WAV up to 10MB, max 30 seconds"}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-white/20 backdrop-blur-md flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-gray-900">Upload Content</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Upload Type Selector */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Content Type
            </label>
            <div className="flex space-x-2">
              <button
                onClick={() => setUploadType("image")}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  uploadType === "image"
                    ? "bg-purple-100 border-purple-500 text-purple-700"
                    : "border-gray-300 hover:border-gray-400"
                }`}
              >
                <Image size={16} />
                <span>Image</span>
              </button>
              <button
                onClick={() => setUploadType("text")}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  uploadType === "text"
                    ? "bg-purple-100 border-purple-500 text-purple-700"
                    : "border-gray-300 hover:border-gray-400"
                }`}
              >
                <Type size={16} />
                <span>Text</span>
              </button>
              <button
                onClick={() => setUploadType("audio")}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  uploadType === "audio"
                    ? "bg-purple-100 border-purple-500 text-purple-700"
                    : "border-gray-300 hover:border-gray-400"
                }`}
              >
                <Music size={16} />
                <span>Audio</span>
              </button>
            </div>
          </div>

          {/* Title Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Give your content a catchy title..."
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Upload Area */}
          {renderUploadArea()}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Submit Button */}
          <button
            onClick={handleSubmit}
            disabled={
              uploading ||
              !title.trim() ||
              (uploadType !== "text" && !selectedFile) ||
              (uploadType === "text" && !textContent.trim())
            }
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors"
          >
            {uploading ? "Uploading..." : "Upload"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadModal;
