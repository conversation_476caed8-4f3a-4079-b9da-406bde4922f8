import { useState } from "react";
import {
  <PERSON>,
  MessageCircle,
  Share2,
  <PERSON><PERSON>,
  <PERSON>,
  User,
  <PERSON>itBran<PERSON>,
  <PERSON>,
} from "lucide-react";
import type { Post } from "../data/mockData";

interface PostCardProps {
  post: Post;
  onRemix: (post: Post) => void;
}

const PostCard = ({ post, onRemix }: PostCardProps) => {
  const [liked, setLiked] = useState(false);
  const [showLineage, setShowLineage] = useState(false);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return new Date(date).toLocaleDateString();
  };

  const renderContent = () => {
    switch (post.type) {
      case "image":
        return (
          <div className="relative">
            <img
              src={post.content}
              alt={post.title}
              className="w-full h-64 object-cover rounded-lg"
              onError={(e) => {
                // Fallback for broken images
                e.currentTarget.src =
                  "https://via.placeholder.com/400x300?text=Image+Not+Found";
              }}
            />
            {post.isRemix && (
              <div className="absolute top-2 left-2 bg-purple-600 text-white px-2 py-1 rounded-md text-xs font-medium">
                Remix
              </div>
            )}
          </div>
        );

      case "text":
        return (
          <div
            className={`p-6 rounded-lg border-2 border-dashed border-gray-200 ${
              post.metadata?.effects?.includes("rainbow_text")
                ? "rainbow-text"
                : ""
            } ${
              post.metadata?.effects?.includes("shake_animation")
                ? "shake-animation"
                : ""
            }`}
          >
            <p className="text-lg leading-relaxed">{post.content}</p>
            {post.isRemix && (
              <div className="mt-4 text-sm text-purple-600 font-medium">
                ✨ Remixed with effects
                {post.metadata?.audioTrack && (
                  <span className="ml-2">🎵 + Audio</span>
                )}
              </div>
            )}
          </div>
        );

      case "audio":
        return (
          <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-6 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-white rounded-full"></div>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{post.title}</h4>
                <p className="text-sm text-gray-600">
                  {post.metadata?.duration
                    ? `${post.metadata.duration}s`
                    : "Audio track"}
                </p>
                {post.isRemix && (
                  <p className="text-sm text-purple-600 font-medium">
                    🎵 Remixed with background music
                  </p>
                )}
              </div>
              <button className="w-10 h-10 bg-purple-600 text-white rounded-full flex items-center justify-center hover:bg-purple-700 transition-colors">
                ▶
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <User size={20} className="text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{post.author}</h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Clock size={14} />
              <span>{formatDate(post.createdAt)}</span>
            </div>
          </div>
        </div>

        {post.isRemix && post.parentId && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-purple-700">
                <GitBranch size={14} />
                <span className="font-medium">
                  Remixed from Post #{post.parentId}
                </span>
              </div>
              <button
                onClick={() => setShowLineage(!showLineage)}
                className="text-purple-600 hover:text-purple-800 text-xs flex items-center space-x-1"
              >
                <Eye size={12} />
                <span>{showLineage ? "Hide" : "Show"} Changes</span>
              </button>
            </div>

            {showLineage && (
              <div className="mt-3 pt-3 border-t border-purple-200">
                <div className="text-xs text-purple-600 space-y-1">
                  {post.metadata?.effects &&
                    post.metadata.effects.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Effects Applied:</span>
                        <div className="flex flex-wrap gap-1">
                          {post.metadata.effects.map((effect, index) => (
                            <span
                              key={index}
                              className="bg-purple-100 px-2 py-1 rounded text-xs"
                            >
                              {effect.replace("_", " ")}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  {post.metadata?.audioTrack && (
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Audio Added:</span>
                      <span className="bg-blue-100 px-2 py-1 rounded text-xs">
                        Background Music
                      </span>
                    </div>
                  )}
                  {post.type === "image" &&
                    post.metadata?.effects?.includes("custom_edit") && (
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Visual Changes:</span>
                        <span className="bg-green-100 px-2 py-1 rounded text-xs">
                          Custom Edits Applied
                        </span>
                      </div>
                    )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Title */}
      <h2 className="text-xl font-bold text-gray-900 mb-4">{post.title}</h2>

      {/* Content */}
      {renderContent()}

      {/* Actions */}
      <div className="flex items-center justify-between mt-6 pt-4 border-t">
        <div className="flex items-center space-x-6">
          <button
            onClick={() => setLiked(!liked)}
            className={`flex items-center space-x-2 transition-colors ${
              liked ? "text-red-500" : "text-gray-700 hover:text-red-500"
            }`}
          >
            <Heart size={20} fill={liked ? "currentColor" : "none"} />
            <span className="text-sm">Like</span>
          </button>

          <button className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors">
            <MessageCircle size={20} />
            <span className="text-sm">Comment</span>
          </button>

          <button className="flex items-center space-x-2 text-gray-700 hover:text-green-600 transition-colors">
            <Share2 size={20} />
            <span className="text-sm">Share</span>
          </button>
        </div>

        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            {post.remixCount} remix{post.remixCount !== 1 ? "es" : ""}
          </span>
          <button
            onClick={() => onRemix(post)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Shuffle size={16} />
            <span>Remix This!</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostCard;
